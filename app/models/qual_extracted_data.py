import sqlalchemy as sa
from sqlalchemy.orm import relationship

from constants.extracted_data import DataSourceType
from core.db import Base


__all__ = ['QualExtractedData']


class QualExtractedData(Base):
    __tablename__ = 'QualExtractedData'

    Id = sa.Column(sa.Integer, primary_key=True, autoincrement=True)
    QualConversationId = sa.Column(sa.Integer, sa.<PERSON>ey('QualConversation.Id'), nullable=False)
    DataSourceType = sa.Column(sa.Enum(DataSourceType, values_callable=lambda x: [e.value for e in x]), nullable=False)
    CreatedAt = sa.Column(sa.DateTime, server_default=sa.func.now(), nullable=False)

    ActivityName = sa.Column(sa.UnicodeText, nullable=True)
    ClientName = sa.Column(sa.UnicodeText, nullable=True)
    LDMFCountry = sa.Column(sa.UnicodeText, nullable=True)
    Title = sa.Column(sa.UnicodeText, nullable=True)
    StartDate = sa.Column(sa.Date, nullable=True)
    EndDate = sa.Column(sa.Date, nullable=True)
    ClientIndustry = sa.Column(sa.UnicodeText, nullable=True)
    ClientServices = sa.Column(sa.UnicodeText, nullable=True)
    TeamAndRoles = sa.Column(sa.UnicodeText, nullable=True)

    Conversation = relationship('QualConversation', back_populates='ExtractedData')

    __table_args__ = (sa.UniqueConstraint('QualConversationId', 'DataSourceType', name='uq_conversation_source_type'),)
