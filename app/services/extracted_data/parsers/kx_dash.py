from datetime import date
import json
import logging
from typing import Any

from schemas import ExtractedData


__all__ = ['KXDashDataParser']


logger = logging.getLogger(__name__)


class KXDashDataParser:
    @classmethod
    def __call__(cls, extracted_data: ExtractedData, activity_data: dict[str, Any]) -> ExtractedData:
        result = extracted_data.copy()
        result.activity_name = activity_data.get('activity_name')
        result.client_name = [val] if (val := activity_data.get('client_name')) else []
        result.ldmf_country = [val] if (val := activity_data.get('country')) else []
        # TODO: populate result with title
        result.start_date = cls._get_date_for_extracted_data(activity_data.get('engagement_start_date'))
        result.end_date = cls._get_date_for_extracted_data(activity_data.get('engagement_end_date'))
        result.client_industry = cls._get_industry_for_extracted_data(activity_data)
        result.client_services = cls._get_services_for_extracted_data(activity_data)
        result.team_and_roles = cls._get_team_and_roles_for_extracted_data(activity_data)
        return result

    @staticmethod
    def _get_date_for_extracted_data(val: Any) -> date | None:
        if val is None or isinstance(val, date):
            result = val
        if isinstance(val, str):
            try:
                result = date.fromisoformat(val)
            except ValueError:
                logger.warning('An activity date of an unexpected format was detected: %s', val)
                result = None
        else:
            logger.warning('An activity date of an unexpected type was detected: %s', type(val))
            result = None
        return result

    @staticmethod
    def _get_industry_for_extracted_data(activity_data: dict[str, Any]) -> str | None:
        result = {}
        if global_industry := activity_data.get('global_industry'):
            result['level_1'] = global_industry
        if global_industry_sector := activity_data.get('global_industry_sector'):
            result['level_2'] = global_industry_sector
        return json.dumps(result) if result else None

    @staticmethod
    def _get_services_for_extracted_data(activity_data: dict[str, Any]) -> str | None:
        result = {}
        if global_business := activity_data.get('global_business'):
            result['level_1'] = global_business
        if global_business_service_area := activity_data.get('global_business_service_area'):
            result['level_2'] = global_business_service_area
        if global_business_service_line := activity_data.get('global_business_service_line'):
            result['level_3'] = global_business_service_line
        return json.dumps(result) if result else None

    @staticmethod
    def _get_team_and_roles_for_extracted_data(activity_data: dict[str, Any]) -> str | None:
        result = {}
        if global_lcsp_emails := activity_data.get('global_lcsp_emails'):
            result['global_lcsp'] = global_lcsp_emails
        if engagement_lep_emails := activity_data.get('engagement_lep_emails'):
            result['engagement_lep'] = engagement_lep_emails
        if engagement_manager_emails := activity_data.get('engagement_manager_emails'):
            result['engagement_manager'] = engagement_manager_emails
        return json.dumps(result) if result else None
