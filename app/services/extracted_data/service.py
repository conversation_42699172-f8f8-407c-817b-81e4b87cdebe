import logging
from typing import Any
from uuid import UUID

from constants.extracted_data import DataSourceType
from repositories import ExtractedDataRepository
from schemas import ExtractedData

from .parsers import KXDashDataParser


__all__ = ['ExtractedDataService']


logger = logging.getLogger(__name__)


class ExtractedDataService:
    _SOURCE_TYPE_TO_PARSER_MAP = {
        DataSourceType.KX_DASH: KXDashDataParser,
    }

    def __init__(self, extracted_data_repository: ExtractedDataRepository):
        self.extracted_data_repository = extracted_data_repository

    async def delete_many(self, conversation_id: UUID) -> None:
        """
        Delete all extracted data for a conversation.

        Args:
            conversation_id: The ID of the conversation to delete extracted data from.

        Raises:
            EntityNotFoundError: If the conversation does not exist.
        """
        logger.debug('Deleting extracted data for conversation ID: %s', conversation_id)
        try:
            await self.extracted_data_repository.delete_many(conversation_id)
        except Exception as e:
            logger.error("Failed to delete extracted data for conversation ID '%s': %s", conversation_id, e)
            raise

    async def update(
        self, conversation_id: UUID, raw_data: dict[str, Any], source_type: DataSourceType
    ) -> ExtractedData:
        """
        Update KX Dash extracted data for a conversation.

        Args:
            conversation_id: The ID of the conversation to update extracted data for.
            activity_data: The data to update the extracted data with.

        Raises:
            EntityNotFoundError: If the conversation does not exist.
        """
        logger.debug('Started updating "%s" extracted data for conversation ID "%s"', source_type, conversation_id)
        try:
            extracted_data = await self.extracted_data_repository.get(
                conversation_id=conversation_id,
                data_source_type=source_type,
            ) or ExtractedData.create(conversation_id=conversation_id, data_source_type=source_type)
            parser = self._SOURCE_TYPE_TO_PARSER_MAP[source_type]()
            await self.extracted_data_repository.update(parser(extracted_data, raw_data))
            return extracted_data

        except Exception as e:
            logger.error(
                'Failed to update "%s" extracted data for conversation ID "%s": %s', source_type, conversation_id, e
            )
            raise
